# LoGaCo - Local Game Connect

A React Native app built with Expo Router that enables local multiplayer gaming without internet connectivity.

## 🎮 Features

- **Local Multiplayer Gaming**: Connect with nearby players without internet
- **Multiple Connection Methods**: Bluetooth, WiFi Direct, and QR codes
- **Game Discovery**: Automatically detect compatible games
- **Beautiful UI**: Glassmorphism design with smooth animations
- **Cross-Platform**: Works on both iOS and Android

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (or physical device with Expo Go)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd LoGaCo
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npx expo start
```

4. Scan the QR code with Expo Go (Android) or Camera app (iOS)

## 📱 App Structure

```
app/
├── (tabs)/                 # Tab navigation
│   ├── index.tsx          # Home screen
│   ├── games.tsx          # Games discovery
│   ├── connect.tsx        # Connection manager
│   └── settings.tsx       # Settings
├── _layout.tsx            # Root layout
└── modal.tsx              # Modal screen

components/
├── ui/                    # Reusable UI components
│   ├── Card.tsx          # Glassmorphism card
│   └── Button.tsx        # Custom button

constants/
├── Colors.ts              # Color palette
└── Layout.ts              # Layout constants
```

## 🎨 Design System

The app uses a glassmorphism design with:
- Dark gradient backgrounds
- Frosted glass effects using expo-blur
- Cyan blue accent color (#00D4FF)
- Smooth animations and transitions

## 🔧 Tech Stack

- **React Native** with Expo
- **Expo Router** for file-based routing
- **TypeScript** for type safety
- **expo-blur** for glassmorphism effects
- **expo-linear-gradient** for gradient backgrounds
- **@expo/vector-icons** for iconography

## 📋 Development Progress

### ✅ Completed
- [x] Project setup with Expo Router
- [x] Glassmorphism UI design system
- [x] Core navigation structure
- [x] Home, Games, Connect, and Settings screens
- [x] Reusable UI components
- [x] Constants for colors and layout

### 🔄 In Progress
- [ ] Network connectivity implementation
- [ ] Game detection system
- [ ] State management with Redux Toolkit

### 📅 Planned
- [ ] Bluetooth connectivity
- [ ] WiFi Direct support
- [ ] QR code connection
- [ ] Game session management
- [ ] User profiles and preferences

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- UI inspiration from modern smart home apps
- Glassmorphism design trends
- Expo team for excellent tooling
