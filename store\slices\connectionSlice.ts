import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Device {
  id: string;
  name: string;
  type: 'ios' | 'android' | 'windows' | 'mac' | 'unknown';
  distance?: string;
  status: 'available' | 'busy' | 'offline' | 'connecting' | 'connected';
  signal: 'strong' | 'medium' | 'weak' | 'none';
  lastSeen: number;
  connectionType: 'bluetooth' | 'wifi' | 'hotspot' | 'unknown';
  capabilities: string[];
}

export interface ConnectionState {
  // Connection status
  isScanning: boolean;
  bluetoothEnabled: boolean;
  wifiEnabled: boolean;
  locationEnabled: boolean;
  
  // Devices
  nearbyDevices: Device[];
  connectedDevices: Device[];
  connectionHistory: Device[];
  
  // Current connection
  activeConnection: Device | null;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  connectionError: string | null;
  
  // Settings
  autoConnect: boolean;
  connectionTimeout: number;
  maxConnections: number;
  
  // Scanning
  scanDuration: number;
  lastScanTime: number | null;
}

const initialState: ConnectionState = {
  // Connection status
  isScanning: false,
  bluetoothEnabled: false,
  wifiEnabled: false,
  locationEnabled: false,
  
  // Devices
  nearbyDevices: [],
  connectedDevices: [],
  connectionHistory: [],
  
  // Current connection
  activeConnection: null,
  connectionStatus: 'disconnected',
  connectionError: null,
  
  // Settings
  autoConnect: false,
  connectionTimeout: 30000, // 30 seconds
  maxConnections: 4,
  
  // Scanning
  scanDuration: 30000, // 30 seconds
  lastScanTime: null,
};

const connectionSlice = createSlice({
  name: 'connection',
  initialState,
  reducers: {
    // Scanning actions
    startScanning: (state) => {
      state.isScanning = true;
      state.lastScanTime = Date.now();
      state.connectionError = null;
    },
    
    stopScanning: (state) => {
      state.isScanning = false;
    },
    
    // Device management
    addNearbyDevice: (state, action: PayloadAction<Device>) => {
      const existingIndex = state.nearbyDevices.findIndex(
        device => device.id === action.payload.id
      );
      
      if (existingIndex >= 0) {
        state.nearbyDevices[existingIndex] = action.payload;
      } else {
        state.nearbyDevices.push(action.payload);
      }
    },
    
    removeNearbyDevice: (state, action: PayloadAction<string>) => {
      state.nearbyDevices = state.nearbyDevices.filter(
        device => device.id !== action.payload
      );
    },
    
    updateDeviceStatus: (state, action: PayloadAction<{ id: string; status: Device['status'] }>) => {
      const { id, status } = action.payload;
      
      // Update in nearby devices
      const nearbyIndex = state.nearbyDevices.findIndex(device => device.id === id);
      if (nearbyIndex >= 0) {
        state.nearbyDevices[nearbyIndex].status = status;
      }
      
      // Update in connected devices
      const connectedIndex = state.connectedDevices.findIndex(device => device.id === id);
      if (connectedIndex >= 0) {
        state.connectedDevices[connectedIndex].status = status;
      }
    },
    
    clearNearbyDevices: (state) => {
      state.nearbyDevices = [];
    },
    
    // Connection actions
    connectToDevice: (state, action: PayloadAction<Device>) => {
      state.connectionStatus = 'connecting';
      state.activeConnection = action.payload;
      state.connectionError = null;
    },
    
    connectionSuccess: (state, action: PayloadAction<Device>) => {
      state.connectionStatus = 'connected';
      state.activeConnection = action.payload;
      state.connectionError = null;
      
      // Add to connected devices if not already there
      const existingIndex = state.connectedDevices.findIndex(
        device => device.id === action.payload.id
      );
      
      if (existingIndex === -1) {
        state.connectedDevices.push(action.payload);
      }
      
      // Add to connection history
      const historyIndex = state.connectionHistory.findIndex(
        device => device.id === action.payload.id
      );
      
      if (historyIndex >= 0) {
        state.connectionHistory[historyIndex] = action.payload;
      } else {
        state.connectionHistory.unshift(action.payload);
        // Keep only last 10 connections
        if (state.connectionHistory.length > 10) {
          state.connectionHistory = state.connectionHistory.slice(0, 10);
        }
      }
    },
    
    connectionError: (state, action: PayloadAction<string>) => {
      state.connectionStatus = 'error';
      state.connectionError = action.payload;
      state.activeConnection = null;
    },
    
    disconnect: (state, action: PayloadAction<string>) => {
      state.connectedDevices = state.connectedDevices.filter(
        device => device.id !== action.payload
      );
      
      if (state.activeConnection?.id === action.payload) {
        state.activeConnection = null;
        state.connectionStatus = 'disconnected';
      }
    },
    
    disconnectAll: (state) => {
      state.connectedDevices = [];
      state.activeConnection = null;
      state.connectionStatus = 'disconnected';
    },
    
    // Settings actions
    setBluetoothEnabled: (state, action: PayloadAction<boolean>) => {
      state.bluetoothEnabled = action.payload;
    },
    
    setWifiEnabled: (state, action: PayloadAction<boolean>) => {
      state.wifiEnabled = action.payload;
    },
    
    setLocationEnabled: (state, action: PayloadAction<boolean>) => {
      state.locationEnabled = action.payload;
    },
    
    setAutoConnect: (state, action: PayloadAction<boolean>) => {
      state.autoConnect = action.payload;
    },
    
    setConnectionTimeout: (state, action: PayloadAction<number>) => {
      state.connectionTimeout = action.payload;
    },
    
    // Clear error
    clearConnectionError: (state) => {
      state.connectionError = null;
    },
  },
});

export const {
  startScanning,
  stopScanning,
  addNearbyDevice,
  removeNearbyDevice,
  updateDeviceStatus,
  clearNearbyDevices,
  connectToDevice,
  connectionSuccess,
  connectionError,
  disconnect,
  disconnectAll,
  setBluetoothEnabled,
  setWifiEnabled,
  setLocationEnabled,
  setAutoConnect,
  setConnectionTimeout,
  clearConnectionError,
} = connectionSlice.actions;

export default connectionSlice.reducer;
