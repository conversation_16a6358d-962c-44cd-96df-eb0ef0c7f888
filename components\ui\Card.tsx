import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  blurIntensity?: number;
  gradient?: boolean;
  gradientColors?: string[];
}

export default function Card({ 
  children, 
  style, 
  blurIntensity = 15,
  gradient = false,
  gradientColors = ['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']
}: CardProps) {
  if (gradient) {
    return (
      <BlurView intensity={blurIntensity} style={[styles.container, style]}>
        <LinearGradient
          colors={gradientColors}
          style={styles.gradient}
        >
          {children}
        </LinearGradient>
      </BlurView>
    );
  }

  return (
    <BlurView intensity={blurIntensity} style={[styles.container, style]}>
      {children}
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
});
