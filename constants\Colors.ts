export const Colors = {
  // Primary colors
  primary: '#00D4FF',
  primaryDark: '#0099CC',
  primaryLight: '#33E0FF',
  
  // Background gradients
  backgroundGradient: ['#1a1a2e', '#16213e', '#0f3460'],
  cardGradient: ['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)'],
  
  // Text colors
  text: {
    primary: '#FFFFFF',
    secondary: 'rgba(255, 255, 255, 0.7)',
    tertiary: 'rgba(255, 255, 255, 0.6)',
    disabled: 'rgba(255, 255, 255, 0.4)',
  },
  
  // Status colors
  success: '#00FF88',
  warning: '#FFB800',
  error: '#FF4757',
  info: '#00D4FF',
  
  // Background colors
  background: {
    primary: '#1a1a2e',
    secondary: '#16213e',
    tertiary: '#0f3460',
    card: 'rgba(255, 255, 255, 0.1)',
    cardHover: 'rgba(255, 255, 255, 0.15)',
  },
  
  // Border colors
  border: {
    primary: 'rgba(255, 255, 255, 0.1)',
    secondary: 'rgba(255, 255, 255, 0.05)',
    focus: '#00D4FF',
  },
  
  // Tab bar colors
  tabBar: {
    background: 'rgba(30, 30, 46, 0.8)',
    backgroundAndroid: 'rgba(30, 30, 46, 0.95)',
    active: '#00D4FF',
    inactive: 'rgba(255, 255, 255, 0.6)',
  },
};

export const Gradients = {
  primary: ['#00D4FF', '#0099CC'],
  background: ['#1a1a2e', '#16213e', '#0f3460'],
  card: ['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)'],
  cardHover: ['rgba(0, 212, 255, 0.15)', 'rgba(0, 212, 255, 0.08)'],
  success: ['#00FF88', '#00CC6A'],
  warning: ['#FFB800', '#E6A500'],
  error: ['#FF4757', '#E63946'],
};

export const BlurIntensity = {
  light: 10,
  medium: 15,
  strong: 20,
  heavy: 30,
};

export default Colors;
