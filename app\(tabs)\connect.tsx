import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Switch,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAppDispatch } from "../../hooks/redux";
import {
  useConnection,
  useNearbyDevices,
  useIsScanning,
} from "../../hooks/redux";
import {
  startScanning,
  stopScanning,
  setBluetoothEnabled,
  setWifiEnabled,
} from "../../store/slices/connectionSlice";

const { width, height } = Dimensions.get("window");

// Mock nearby devices
const mockDevices = [
  {
    id: 1,
    name: "Alex's iPhone",
    type: "ios",
    distance: "2m away",
    status: "available",
    signal: "strong",
  },
  {
    id: 2,
    name: "Gaming Laptop",
    type: "windows",
    distance: "5m away",
    status: "busy",
    signal: "medium",
  },
  {
    id: 3,
    name: "Sarah's Android",
    type: "android",
    distance: "8m away",
    status: "available",
    signal: "weak",
  },
];

export default function ConnectScreen() {
  const dispatch = useAppDispatch();
  const connection = useConnection();
  const nearbyDevices = useNearbyDevices();
  const isScanning = useIsScanning();

  const { bluetoothEnabled, wifiEnabled } = connection;

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case "ios":
        return "phone-portrait";
      case "android":
        return "phone-portrait";
      case "windows":
        return "laptop";
      default:
        return "device-desktop";
    }
  };

  const getSignalIcon = (signal: string) => {
    switch (signal) {
      case "strong":
        return "wifi";
      case "medium":
        return "wifi-outline";
      case "weak":
        return "cellular-outline";
      default:
        return "wifi-off";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "#00FF88";
      case "busy":
        return "#FFB800";
      case "offline":
        return "#FF4757";
      default:
        return "#00D4FF";
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Connect</Text>
            <TouchableOpacity style={styles.qrButton}>
              <Ionicons name="qr-code" size={24} color="#00D4FF" />
            </TouchableOpacity>
          </View>

          {/* Connection Status */}
          <BlurView intensity={15} style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Text style={styles.statusTitle}>Connection Status</Text>
              <TouchableOpacity
                style={[
                  styles.scanButton,
                  isScanning && styles.scanButtonActive,
                ]}
                onPress={() =>
                  dispatch(isScanning ? stopScanning() : startScanning())
                }
              >
                <Ionicons
                  name={isScanning ? "stop" : "search"}
                  size={20}
                  color={isScanning ? "#1a1a2e" : "#00D4FF"}
                />
                <Text
                  style={[
                    styles.scanButtonText,
                    isScanning && styles.scanButtonTextActive,
                  ]}
                >
                  {isScanning ? "Stop" : "Scan"}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.connectionOptions}>
              <View style={styles.connectionOption}>
                <View style={styles.optionInfo}>
                  <Ionicons name="bluetooth" size={24} color="#00D4FF" />
                  <Text style={styles.optionText}>Bluetooth</Text>
                </View>
                <Switch
                  value={bluetoothEnabled}
                  onValueChange={(value) =>
                    dispatch(setBluetoothEnabled(value))
                  }
                  trackColor={{ false: "#767577", true: "#00D4FF" }}
                  thumbColor={bluetoothEnabled ? "#FFFFFF" : "#f4f3f4"}
                />
              </View>

              <View style={styles.connectionOption}>
                <View style={styles.optionInfo}>
                  <Ionicons name="wifi" size={24} color="#00D4FF" />
                  <Text style={styles.optionText}>Wi-Fi Direct</Text>
                </View>
                <Switch
                  value={wifiEnabled}
                  onValueChange={(value) => dispatch(setWifiEnabled(value))}
                  trackColor={{ false: "#767577", true: "#00D4FF" }}
                  thumbColor={wifiEnabled ? "#FFFFFF" : "#f4f3f4"}
                />
              </View>
            </View>
          </BlurView>

          {/* Nearby Devices */}
          <View style={styles.devicesSection}>
            <Text style={styles.sectionTitle}>Nearby Devices</Text>
            {isScanning && (
              <View style={styles.scanningIndicator}>
                <Text style={styles.scanningText}>Scanning for devices...</Text>
              </View>
            )}

            <View style={styles.devicesList}>
              {nearbyDevices.map((device) => (
                <TouchableOpacity key={device.id} style={styles.deviceCard}>
                  <BlurView intensity={15} style={styles.deviceBlur}>
                    <View style={styles.deviceContent}>
                      <View style={styles.deviceIcon}>
                        <Ionicons
                          name={getDeviceIcon(device.type) as any}
                          size={28}
                          color="#00D4FF"
                        />
                      </View>
                      <View style={styles.deviceInfo}>
                        <Text style={styles.deviceName}>{device.name}</Text>
                        <Text style={styles.deviceDistance}>
                          {device.distance}
                        </Text>
                      </View>
                      <View style={styles.deviceStatus}>
                        <Ionicons
                          name={getSignalIcon(device.signal) as any}
                          size={20}
                          color="rgba(255, 255, 255, 0.6)"
                        />
                        <View
                          style={[
                            styles.statusIndicator,
                            { backgroundColor: getStatusColor(device.status) },
                          ]}
                        />
                      </View>
                    </View>
                  </BlurView>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Quick Connect Options */}
          <View style={styles.quickConnect}>
            <Text style={styles.sectionTitle}>Quick Connect</Text>
            <View style={styles.quickConnectGrid}>
              <TouchableOpacity style={styles.quickConnectCard}>
                <BlurView intensity={15} style={styles.quickConnectBlur}>
                  <Ionicons name="qr-code-outline" size={32} color="#00D4FF" />
                  <Text style={styles.quickConnectText}>QR Code</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity style={styles.quickConnectCard}>
                <BlurView intensity={15} style={styles.quickConnectBlur}>
                  <Ionicons name="link-outline" size={32} color="#00D4FF" />
                  <Text style={styles.quickConnectText}>Share Link</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  qrButton: {
    padding: 8,
  },
  statusCard: {
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 16,
    overflow: "hidden",
    padding: 20,
  },
  statusHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  scanButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    gap: 6,
  },
  scanButtonActive: {
    backgroundColor: "#00D4FF",
  },
  scanButtonText: {
    fontSize: 14,
    color: "#00D4FF",
    fontWeight: "600",
  },
  scanButtonTextActive: {
    color: "#1a1a2e",
  },
  connectionOptions: {
    gap: 16,
  },
  connectionOption: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  optionInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  optionText: {
    fontSize: 16,
    color: "#FFFFFF",
  },
  devicesSection: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 16,
  },
  scanningIndicator: {
    alignItems: "center",
    marginBottom: 16,
  },
  scanningText: {
    fontSize: 14,
    color: "#00D4FF",
    fontStyle: "italic",
  },
  devicesList: {
    gap: 12,
  },
  deviceCard: {
    borderRadius: 16,
    overflow: "hidden",
  },
  deviceBlur: {
    padding: 16,
  },
  deviceContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  deviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  deviceDistance: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  deviceStatus: {
    alignItems: "center",
    gap: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  quickConnect: {
    paddingHorizontal: 20,
  },
  quickConnectGrid: {
    flexDirection: "row",
    gap: 12,
  },
  quickConnectCard: {
    flex: 1,
    borderRadius: 16,
    overflow: "hidden",
  },
  quickConnectBlur: {
    padding: 20,
    alignItems: "center",
    gap: 8,
  },
  quickConnectText: {
    fontSize: 14,
    color: "#FFFFFF",
    textAlign: "center",
  },
});
