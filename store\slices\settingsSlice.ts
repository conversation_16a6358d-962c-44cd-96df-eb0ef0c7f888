import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface AppSettings {
  // Theme and appearance
  theme: 'dark' | 'light' | 'auto';
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
  reducedMotion: boolean;
  
  // Notifications
  notifications: {
    enabled: boolean;
    connectionAlerts: boolean;
    gameInvites: boolean;
    sessionUpdates: boolean;
    systemMessages: boolean;
    sound: boolean;
    vibration: boolean;
    quietHours: {
      enabled: boolean;
      startTime: string; // HH:MM format
      endTime: string; // HH:MM format
    };
  };
  
  // Connection settings
  connection: {
    autoConnect: boolean;
    timeout: number;
    retryAttempts: number;
    preferredMethod: 'bluetooth' | 'wifi' | 'auto';
    maxConnections: number;
    keepAlive: boolean;
  };
  
  // Privacy and security
  privacy: {
    deviceVisibility: 'everyone' | 'contacts' | 'nobody';
    shareLocation: boolean;
    shareDeviceInfo: boolean;
    shareGameActivity: boolean;
    allowInvitations: boolean;
    blockUnknownDevices: boolean;
  };
  
  // Game settings
  game: {
    autoLaunch: boolean;
    closeOnDisconnect: boolean;
    saveGameHistory: boolean;
    maxHistoryEntries: number;
    defaultSessionSettings: Record<string, any>;
  };
  
  // Developer settings
  developer: {
    debugMode: boolean;
    showPerformanceMetrics: boolean;
    enableLogging: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    mockData: boolean;
  };
  
  // Accessibility
  accessibility: {
    screenReader: boolean;
    highContrast: boolean;
    largeText: boolean;
    reduceTransparency: boolean;
    buttonShapes: boolean;
  };
}

export interface SettingsState {
  settings: AppSettings;
  
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  
  // Sync
  lastSyncTime: number | null;
  needsSync: boolean;
  
  // Errors
  error: string | null;
  
  // Reset confirmation
  showResetConfirmation: boolean;
}

const initialState: SettingsState = {
  settings: {
    // Theme and appearance
    theme: 'dark',
    accentColor: '#00D4FF',
    fontSize: 'medium',
    reducedMotion: false,
    
    // Notifications
    notifications: {
      enabled: true,
      connectionAlerts: true,
      gameInvites: true,
      sessionUpdates: true,
      systemMessages: true,
      sound: true,
      vibration: true,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    },
    
    // Connection settings
    connection: {
      autoConnect: false,
      timeout: 30000,
      retryAttempts: 3,
      preferredMethod: 'auto',
      maxConnections: 4,
      keepAlive: true,
    },
    
    // Privacy and security
    privacy: {
      deviceVisibility: 'everyone',
      shareLocation: false,
      shareDeviceInfo: true,
      shareGameActivity: true,
      allowInvitations: true,
      blockUnknownDevices: false,
    },
    
    // Game settings
    game: {
      autoLaunch: false,
      closeOnDisconnect: true,
      saveGameHistory: true,
      maxHistoryEntries: 100,
      defaultSessionSettings: {},
    },
    
    // Developer settings
    developer: {
      debugMode: false,
      showPerformanceMetrics: false,
      enableLogging: true,
      logLevel: 'warn',
      mockData: __DEV__,
    },
    
    // Accessibility
    accessibility: {
      screenReader: false,
      highContrast: false,
      largeText: false,
      reduceTransparency: false,
      buttonShapes: false,
    },
  },
  
  // Loading states
  isLoading: false,
  isSaving: false,
  
  // Sync
  lastSyncTime: null,
  needsSync: false,
  
  // Errors
  error: null,
  
  // Reset confirmation
  showResetConfirmation: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // General settings updates
    updateSettings: (state, action: PayloadAction<Partial<AppSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
      state.needsSync = true;
    },
    
    // Theme settings
    setTheme: (state, action: PayloadAction<AppSettings['theme']>) => {
      state.settings.theme = action.payload;
      state.needsSync = true;
    },
    
    setAccentColor: (state, action: PayloadAction<string>) => {
      state.settings.accentColor = action.payload;
      state.needsSync = true;
    },
    
    setFontSize: (state, action: PayloadAction<AppSettings['fontSize']>) => {
      state.settings.fontSize = action.payload;
      state.needsSync = true;
    },
    
    // Notification settings
    updateNotificationSettings: (state, action: PayloadAction<Partial<AppSettings['notifications']>>) => {
      state.settings.notifications = { ...state.settings.notifications, ...action.payload };
      state.needsSync = true;
    },
    
    toggleNotifications: (state, action: PayloadAction<boolean>) => {
      state.settings.notifications.enabled = action.payload;
      state.needsSync = true;
    },
    
    // Connection settings
    updateConnectionSettings: (state, action: PayloadAction<Partial<AppSettings['connection']>>) => {
      state.settings.connection = { ...state.settings.connection, ...action.payload };
      state.needsSync = true;
    },
    
    setAutoConnect: (state, action: PayloadAction<boolean>) => {
      state.settings.connection.autoConnect = action.payload;
      state.needsSync = true;
    },
    
    setConnectionTimeout: (state, action: PayloadAction<number>) => {
      state.settings.connection.timeout = action.payload;
      state.needsSync = true;
    },
    
    // Privacy settings
    updatePrivacySettings: (state, action: PayloadAction<Partial<AppSettings['privacy']>>) => {
      state.settings.privacy = { ...state.settings.privacy, ...action.payload };
      state.needsSync = true;
    },
    
    setDeviceVisibility: (state, action: PayloadAction<AppSettings['privacy']['deviceVisibility']>) => {
      state.settings.privacy.deviceVisibility = action.payload;
      state.needsSync = true;
    },
    
    // Game settings
    updateGameSettings: (state, action: PayloadAction<Partial<AppSettings['game']>>) => {
      state.settings.game = { ...state.settings.game, ...action.payload };
      state.needsSync = true;
    },
    
    // Developer settings
    updateDeveloperSettings: (state, action: PayloadAction<Partial<AppSettings['developer']>>) => {
      state.settings.developer = { ...state.settings.developer, ...action.payload };
      state.needsSync = true;
    },
    
    toggleDebugMode: (state, action: PayloadAction<boolean>) => {
      state.settings.developer.debugMode = action.payload;
      state.needsSync = true;
    },
    
    // Accessibility settings
    updateAccessibilitySettings: (state, action: PayloadAction<Partial<AppSettings['accessibility']>>) => {
      state.settings.accessibility = { ...state.settings.accessibility, ...action.payload };
      state.needsSync = true;
    },
    
    // Loading states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setSaving: (state, action: PayloadAction<boolean>) => {
      state.isSaving = action.payload;
    },
    
    // Sync
    syncCompleted: (state) => {
      state.lastSyncTime = Date.now();
      state.needsSync = false;
      state.isSaving = false;
    },
    
    syncFailed: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isSaving = false;
    },
    
    // Reset
    showResetConfirmation: (state, action: PayloadAction<boolean>) => {
      state.showResetConfirmation = action.payload;
    },
    
    resetToDefaults: (state) => {
      const currentTheme = state.settings.theme;
      const currentAccentColor = state.settings.accentColor;
      
      state.settings = initialState.settings;
      
      // Preserve some user preferences
      state.settings.theme = currentTheme;
      state.settings.accentColor = currentAccentColor;
      
      state.needsSync = true;
      state.showResetConfirmation = false;
    },
    
    resetSection: (state, action: PayloadAction<keyof AppSettings>) => {
      const section = action.payload;
      state.settings[section] = initialState.settings[section];
      state.needsSync = true;
    },
    
    // Error handling
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
      state.isSaving = false;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    // Import/Export
    importSettings: (state, action: PayloadAction<AppSettings>) => {
      state.settings = action.payload;
      state.needsSync = true;
    },
    
    // Backup
    createBackup: (state) => {
      // This would trigger a backup creation in middleware
      state.lastSyncTime = Date.now();
    },
  },
});

export const {
  updateSettings,
  setTheme,
  setAccentColor,
  setFontSize,
  updateNotificationSettings,
  toggleNotifications,
  updateConnectionSettings,
  setAutoConnect,
  setConnectionTimeout,
  updatePrivacySettings,
  setDeviceVisibility,
  updateGameSettings,
  updateDeveloperSettings,
  toggleDebugMode,
  updateAccessibilitySettings,
  setLoading,
  setSaving,
  syncCompleted,
  syncFailed,
  showResetConfirmation,
  resetToDefaults,
  resetSection,
  setError,
  clearError,
  importSettings,
  createBackup,
} = settingsSlice.actions;

export default settingsSlice.reducer;
