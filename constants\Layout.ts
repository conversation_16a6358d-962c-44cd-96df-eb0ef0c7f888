import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const Layout = {
  window: {
    width,
    height,
  },
  
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Border radius
  radius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    round: 50,
  },
  
  // Typography
  typography: {
    h1: 32,
    h2: 28,
    h3: 24,
    h4: 20,
    h5: 18,
    h6: 16,
    body: 16,
    bodySmall: 14,
    caption: 12,
    overline: 10,
  },
  
  // Component sizes
  button: {
    small: {
      height: 36,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    medium: {
      height: 48,
      paddingHorizontal: 24,
      paddingVertical: 12,
    },
    large: {
      height: 56,
      paddingHorizontal: 32,
      paddingVertical: 16,
    },
  },
  
  input: {
    height: 48,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  
  card: {
    padding: 16,
    margin: 8,
  },
  
  // Tab bar
  tabBar: {
    height: 90,
    paddingBottom: 20,
    paddingTop: 10,
  },
  
  // Header
  header: {
    height: 60,
    paddingHorizontal: 20,
  },
  
  // Safe areas
  safeArea: {
    paddingBottom: 120, // Account for tab bar
  },
  
  // Grid
  grid: {
    columns: 2,
    gap: 12,
    itemWidth: (width - 56) / 2, // Account for padding and gap
  },
};

export const isSmallScreen = width < 375;
export const isMediumScreen = width >= 375 && width < 414;
export const isLargeScreen = width >= 414;

export default Layout;
