import { Device } from '../store/slices/connectionSlice';

// Mock device data for simulation
const mockDevices: Device[] = [
  {
    id: 'device-1',
    name: "<PERSON>'s iPhone",
    type: 'ios',
    distance: '2m away',
    status: 'available',
    signal: 'strong',
    lastSeen: Date.now(),
    connectionType: 'wifi',
    capabilities: ['gaming', 'file-sharing'],
  },
  {
    id: 'device-2',
    name: "Gaming Laptop",
    type: 'windows',
    distance: '5m away',
    status: 'busy',
    signal: 'medium',
    lastSeen: Date.now() - 30000,
    connectionType: 'wifi',
    capabilities: ['gaming', 'streaming'],
  },
  {
    id: 'device-3',
    name: "<PERSON>'s Android",
    type: 'android',
    distance: '8m away',
    status: 'available',
    signal: 'weak',
    lastSeen: Date.now() - 60000,
    connectionType: 'bluetooth',
    capabilities: ['gaming'],
  },
];

export class NetworkService {
  private static instance: NetworkService;
  private scanningInterval: NodeJS.Timeout | null = null;
  private onDeviceFound: ((device: Device) => void) | null = null;
  private onDeviceLost: ((deviceId: string) => void) | null = null;
  private onConnectionStatusChanged: ((status: string) => void) | null = null;

  static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  // Device scanning
  startScanning(
    onDeviceFound: (device: Device) => void,
    onDeviceLost: (deviceId: string) => void
  ): Promise<void> {
    return new Promise((resolve) => {
      this.onDeviceFound = onDeviceFound;
      this.onDeviceLost = onDeviceLost;

      // Simulate device discovery
      let deviceIndex = 0;
      this.scanningInterval = setInterval(() => {
        if (deviceIndex < mockDevices.length) {
          const device = {
            ...mockDevices[deviceIndex],
            lastSeen: Date.now(),
            // Randomize some properties for realism
            signal: this.getRandomSignal(),
            status: this.getRandomStatus(),
          };
          
          this.onDeviceFound?.(device);
          deviceIndex++;
        } else {
          // Occasionally update existing devices
          if (Math.random() > 0.7) {
            const randomDevice = mockDevices[Math.floor(Math.random() * mockDevices.length)];
            const updatedDevice = {
              ...randomDevice,
              lastSeen: Date.now(),
              signal: this.getRandomSignal(),
              status: this.getRandomStatus(),
            };
            this.onDeviceFound?.(updatedDevice);
          }
        }
      }, 2000); // Discover a device every 2 seconds

      resolve();
    });
  }

  stopScanning(): Promise<void> {
    return new Promise((resolve) => {
      if (this.scanningInterval) {
        clearInterval(this.scanningInterval);
        this.scanningInterval = null;
      }
      resolve();
    });
  }

  // Connection management
  connectToDevice(deviceId: string): Promise<Device> {
    return new Promise((resolve, reject) => {
      const device = mockDevices.find(d => d.id === deviceId);
      
      if (!device) {
        reject(new Error('Device not found'));
        return;
      }

      // Simulate connection delay
      setTimeout(() => {
        if (Math.random() > 0.2) { // 80% success rate
          const connectedDevice = {
            ...device,
            status: 'connected' as const,
            lastSeen: Date.now(),
          };
          resolve(connectedDevice);
        } else {
          reject(new Error('Connection failed'));
        }
      }, 3000); // 3 second connection delay
    });
  }

  disconnectFromDevice(deviceId: string): Promise<void> {
    return new Promise((resolve) => {
      // Simulate disconnection delay
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  // Bluetooth operations
  enableBluetooth(): Promise<boolean> {
    return new Promise((resolve) => {
      // Simulate enabling Bluetooth
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  }

  disableBluetooth(): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(false);
      }, 500);
    });
  }

  // WiFi operations
  enableWiFi(): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  }

  disableWiFi(): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(false);
      }, 500);
    });
  }

  // Hotspot operations
  createHotspot(name: string, password: string): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 2000);
    });
  }

  stopHotspot(): Promise<boolean> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 1000);
    });
  }

  // QR Code operations
  generateQRCode(connectionInfo: any): Promise<string> {
    return new Promise((resolve) => {
      // Generate a mock QR code data
      const qrData = JSON.stringify({
        type: 'logaco_connection',
        deviceId: 'current-device',
        connectionInfo,
        timestamp: Date.now(),
      });
      resolve(qrData);
    });
  }

  parseQRCode(qrData: string): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        const data = JSON.parse(qrData);
        if (data.type === 'logaco_connection') {
          resolve(data);
        } else {
          reject(new Error('Invalid QR code'));
        }
      } catch (error) {
        reject(new Error('Failed to parse QR code'));
      }
    });
  }

  // Utility methods
  private getRandomSignal(): 'strong' | 'medium' | 'weak' {
    const signals = ['strong', 'medium', 'weak'];
    return signals[Math.floor(Math.random() * signals.length)] as any;
  }

  private getRandomStatus(): 'available' | 'busy' | 'offline' {
    const statuses = ['available', 'busy'];
    // Mostly available, sometimes busy
    return Math.random() > 0.3 ? 'available' : 'busy';
  }

  // Network status
  getNetworkStatus(): Promise<{
    bluetooth: boolean;
    wifi: boolean;
    location: boolean;
  }> {
    return new Promise((resolve) => {
      resolve({
        bluetooth: true,
        wifi: true,
        location: true,
      });
    });
  }

  // Device info
  getCurrentDeviceInfo(): Promise<Device> {
    return new Promise((resolve) => {
      resolve({
        id: 'current-device',
        name: 'My Device',
        type: 'ios', // This would be detected dynamically
        status: 'available',
        signal: 'strong',
        lastSeen: Date.now(),
        connectionType: 'wifi',
        capabilities: ['gaming', 'file-sharing', 'streaming'],
      });
    });
  }
}

export default NetworkService.getInstance();
