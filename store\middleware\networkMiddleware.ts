import { Middleware } from '@reduxjs/toolkit';
import NetworkService from '../../services/NetworkService';
import {
  startScanning,
  stopScanning,
  addNearbyDevice,
  removeNearbyDevice,
  connectToDevice,
  connectionSuccess,
  connectionError,
  setBluetoothEnabled,
  setWifiEnabled,
} from '../slices/connectionSlice';

export const networkMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Handle network-related actions
  switch (action.type) {
    case startScanning.type:
      handleStartScanning(store);
      break;
      
    case stopScanning.type:
      handleStopScanning();
      break;
      
    case connectToDevice.type:
      handleConnectToDevice(store, action.payload);
      break;
      
    case setBluetoothEnabled.type:
      handleBluetoothToggle(action.payload);
      break;
      
    case setWifiEnabled.type:
      handleWiFiToggle(action.payload);
      break;
  }

  return result;
};

// Scanning handlers
const handleStartScanning = async (store: any) => {
  try {
    await NetworkService.startScanning(
      (device) => {
        store.dispatch(addNearbyDevice(device));
      },
      (deviceId) => {
        store.dispatch(removeNearbyDevice(deviceId));
      }
    );
  } catch (error) {
    console.error('Failed to start scanning:', error);
    store.dispatch(stopScanning());
  }
};

const handleStopScanning = async () => {
  try {
    await NetworkService.stopScanning();
  } catch (error) {
    console.error('Failed to stop scanning:', error);
  }
};

// Connection handlers
const handleConnectToDevice = async (store: any, device: any) => {
  try {
    const connectedDevice = await NetworkService.connectToDevice(device.id);
    store.dispatch(connectionSuccess(connectedDevice));
  } catch (error) {
    console.error('Connection failed:', error);
    store.dispatch(connectionError(error instanceof Error ? error.message : 'Connection failed'));
  }
};

// Bluetooth handlers
const handleBluetoothToggle = async (enabled: boolean) => {
  try {
    if (enabled) {
      await NetworkService.enableBluetooth();
    } else {
      await NetworkService.disableBluetooth();
    }
  } catch (error) {
    console.error('Bluetooth toggle failed:', error);
  }
};

// WiFi handlers
const handleWiFiToggle = async (enabled: boolean) => {
  try {
    if (enabled) {
      await NetworkService.enableWiFi();
    } else {
      await NetworkService.disableWiFi();
    }
  } catch (error) {
    console.error('WiFi toggle failed:', error);
  }
};
