import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  TextInput,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { useAppDispatch } from "../../hooks/redux";
import {
  useGame,
  useFilteredGames,
  useIsGameScanning,
} from "../../hooks/redux";
import {
  startGameScan,
  stopGameScan,
  setSearchQuery,
  setSelectedFilter,
} from "../../store/slices/gameSlice";

const { width, height } = Dimensions.get("window");

// Mock game data
const mockGames = [
  {
    id: 1,
    name: "Among Us",
    players: "4-10 players",
    status: "compatible",
    icon: "people",
  },
  {
    id: 2,
    name: "Minecraft",
    players: "2-8 players",
    status: "compatible",
    icon: "cube",
  },
  {
    id: 3,
    name: "Call of Duty Mobile",
    players: "2-4 players",
    status: "needs_setup",
    icon: "rifle",
  },
  {
    id: 4,
    name: "PUBG Mobile",
    players: "2-4 players",
    status: "incompatible",
    icon: "shield",
  },
];

export default function GamesScreen() {
  const dispatch = useAppDispatch();
  const game = useGame();
  const filteredGames = useFilteredGames();
  const isScanning = useIsGameScanning();

  const { searchQuery, selectedFilter } = game;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compatible":
        return "#00FF88";
      case "needs_setup":
        return "#FFB800";
      case "incompatible":
        return "#FF4757";
      default:
        return "#00D4FF";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "compatible":
        return "Ready to Play";
      case "needs_setup":
        return "Setup Required";
      case "incompatible":
        return "Not Compatible";
      default:
        return "Unknown";
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Games</Text>
            <TouchableOpacity
              style={styles.scanButton}
              onPress={() =>
                dispatch(isScanning ? stopGameScan() : startGameScan())
              }
            >
              <Ionicons
                name={isScanning ? "stop" : "refresh"}
                size={24}
                color="#00D4FF"
              />
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <BlurView intensity={15} style={styles.searchContainer}>
            <Ionicons
              name="search"
              size={20}
              color="rgba(255, 255, 255, 0.6)"
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search games..."
              placeholderTextColor="rgba(255, 255, 255, 0.6)"
              value={searchQuery}
              onChangeText={(text) => dispatch(setSearchQuery(text))}
            />
          </BlurView>

          {/* Filter Tabs */}
          <View style={styles.filterContainer}>
            {["all", "compatible", "needs_setup"].map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterTab,
                  selectedFilter === filter && styles.filterTabActive,
                ]}
                onPress={() => dispatch(setSelectedFilter(filter))}
              >
                <Text
                  style={[
                    styles.filterText,
                    selectedFilter === filter && styles.filterTextActive,
                  ]}
                >
                  {filter === "all"
                    ? "All Games"
                    : filter === "compatible"
                    ? "Compatible"
                    : "Setup Required"}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Games List */}
          <View style={styles.gamesList}>
            {filteredGames.map((game) => (
              <TouchableOpacity key={game.id} style={styles.gameCard}>
                <BlurView intensity={15} style={styles.gameBlur}>
                  <View style={styles.gameContent}>
                    <View style={styles.gameIcon}>
                      <Ionicons
                        name={game.icon as any}
                        size={32}
                        color="#00D4FF"
                      />
                    </View>
                    <View style={styles.gameInfo}>
                      <Text style={styles.gameName}>{game.name}</Text>
                      <Text style={styles.gamePlayers}>{game.players}</Text>
                      <View style={styles.statusContainer}>
                        <View
                          style={[
                            styles.statusDot,
                            { backgroundColor: getStatusColor(game.status) },
                          ]}
                        />
                        <Text
                          style={[
                            styles.statusText,
                            { color: getStatusColor(game.status) },
                          ]}
                        >
                          {getStatusText(game.status)}
                        </Text>
                      </View>
                    </View>
                    <TouchableOpacity style={styles.gameAction}>
                      <Ionicons
                        name="chevron-forward"
                        size={20}
                        color="rgba(255, 255, 255, 0.6)"
                      />
                    </TouchableOpacity>
                  </View>
                </BlurView>
              </TouchableOpacity>
            ))}
          </View>

          {/* Add Game Button */}
          <TouchableOpacity style={styles.addGameButton}>
            <BlurView intensity={15} style={styles.addGameBlur}>
              <Ionicons name="add-circle-outline" size={24} color="#00D4FF" />
              <Text style={styles.addGameText}>Add Custom Game</Text>
            </BlurView>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  scanButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: "hidden",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#FFFFFF",
  },
  filterContainer: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  filterTabActive: {
    backgroundColor: "#00D4FF",
  },
  filterText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.7)",
  },
  filterTextActive: {
    color: "#1a1a2e",
    fontWeight: "600",
  },
  gamesList: {
    paddingHorizontal: 20,
    gap: 12,
  },
  gameCard: {
    borderRadius: 16,
    overflow: "hidden",
  },
  gameBlur: {
    padding: 16,
  },
  gameContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  gameIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  gameInfo: {
    flex: 1,
  },
  gameName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  gamePlayers: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
  },
  gameAction: {
    padding: 8,
  },
  addGameButton: {
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 16,
    overflow: "hidden",
  },
  addGameBlur: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    gap: 12,
  },
  addGameText: {
    fontSize: 16,
    color: "#00D4FF",
    fontWeight: "600",
  },
});
