import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

export default function SettingsScreen() {
  const [notifications, setNotifications] = useState(true);
  const [autoConnect, setAutoConnect] = useState(false);
  const [darkMode, setDarkMode] = useState(true);
  const [analytics, setAnalytics] = useState(true);

  const settingsGroups = [
    {
      title: 'Connection',
      items: [
        {
          icon: 'notifications',
          title: 'Notifications',
          subtitle: 'Get notified when devices are found',
          type: 'switch',
          value: notifications,
          onToggle: setNotifications,
        },
        {
          icon: 'flash',
          title: 'Auto Connect',
          subtitle: 'Automatically connect to known devices',
          type: 'switch',
          value: autoConnect,
          onToggle: setAutoConnect,
        },
        {
          icon: 'time',
          title: 'Connection Timeout',
          subtitle: '30 seconds',
          type: 'navigation',
        },
      ],
    },
    {
      title: 'Appearance',
      items: [
        {
          icon: 'moon',
          title: 'Dark Mode',
          subtitle: 'Use dark theme',
          type: 'switch',
          value: darkMode,
          onToggle: setDarkMode,
        },
        {
          icon: 'color-palette',
          title: 'Theme Color',
          subtitle: 'Cyan Blue',
          type: 'navigation',
        },
      ],
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          icon: 'shield-checkmark',
          title: 'Device Visibility',
          subtitle: 'Visible to nearby devices',
          type: 'navigation',
        },
        {
          icon: 'analytics',
          title: 'Analytics',
          subtitle: 'Help improve the app',
          type: 'switch',
          value: analytics,
          onToggle: setAnalytics,
        },
        {
          icon: 'lock-closed',
          title: 'Privacy Policy',
          subtitle: 'View our privacy policy',
          type: 'navigation',
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          icon: 'help-circle',
          title: 'Help & FAQ',
          subtitle: 'Get help and find answers',
          type: 'navigation',
        },
        {
          icon: 'bug',
          title: 'Report a Bug',
          subtitle: 'Help us improve the app',
          type: 'navigation',
        },
        {
          icon: 'star',
          title: 'Rate the App',
          subtitle: 'Share your feedback',
          type: 'navigation',
        },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    return (
      <TouchableOpacity key={item.title} style={styles.settingItem}>
        <View style={styles.settingIcon}>
          <Ionicons name={item.icon} size={20} color="#00D4FF" />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
        </View>
        <View style={styles.settingAction}>
          {item.type === 'switch' ? (
            <Switch
              value={item.value}
              onValueChange={item.onToggle}
              trackColor={{ false: '#767577', true: '#00D4FF' }}
              thumbColor={item.value ? '#FFFFFF' : '#f4f3f4'}
            />
          ) : (
            <Ionicons name="chevron-forward" size={20} color="rgba(255, 255, 255, 0.4)" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />
      
      <SafeAreaView style={styles.safeArea}>
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Settings</Text>
          </View>

          {/* Profile Section */}
          <BlurView intensity={15} style={styles.profileCard}>
            <View style={styles.profileContent}>
              <View style={styles.avatar}>
                <Ionicons name="person" size={32} color="#00D4FF" />
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>Your Device</Text>
                <Text style={styles.profileSubtitle}>iPhone 15 Pro</Text>
              </View>
              <TouchableOpacity style={styles.editButton}>
                <Ionicons name="pencil" size={20} color="#00D4FF" />
              </TouchableOpacity>
            </View>
          </BlurView>

          {/* Settings Groups */}
          {settingsGroups.map((group) => (
            <View key={group.title} style={styles.settingsGroup}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              <BlurView intensity={15} style={styles.groupCard}>
                {group.items.map((item, index) => (
                  <View key={item.title}>
                    {renderSettingItem(item)}
                    {index < group.items.length - 1 && <View style={styles.separator} />}
                  </View>
                ))}
              </BlurView>
            </View>
          ))}

          {/* App Info */}
          <View style={styles.appInfo}>
            <Text style={styles.appVersion}>LoGaCo v1.0.0</Text>
            <Text style={styles.appSubtitle}>Local Game Connect</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  profileCard: {
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 16,
    overflow: 'hidden',
    padding: 20,
  },
  profileContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  profileSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  editButton: {
    padding: 8,
  },
  settingsGroup: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 12,
    marginLeft: 4,
  },
  groupCard: {
    borderRadius: 16,
    overflow: 'hidden',
    padding: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  settingAction: {
    marginLeft: 16,
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginLeft: 68,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
  },
});
