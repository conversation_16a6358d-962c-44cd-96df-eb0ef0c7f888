import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { combineReducers } from "@reduxjs/toolkit";

// Import slices
import connectionSlice from "./slices/connectionSlice";
import gameSlice from "./slices/gameSlice";
import userSlice from "./slices/userSlice";
import settingsSlice from "./slices/settingsSlice";

// Import middleware
import { networkMiddleware } from "./middleware/networkMiddleware";

// Persist config
const persistConfig = {
  key: "root",
  storage: AsyncStorage,
  whitelist: ["user", "settings"], // Only persist user and settings
};

// Root reducer
const rootReducer = combineReducers({
  connection: connectionSlice,
  game: gameSlice,
  user: userSlice,
  settings: settingsSlice,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }).concat(networkMiddleware),
  devTools: __DEV__,
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
