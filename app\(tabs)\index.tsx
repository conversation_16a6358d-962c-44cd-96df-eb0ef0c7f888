import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

export default function HomeScreen() {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />
      
      <SafeAreaView style={styles.safeArea}>
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View>
              <Text style={styles.greeting}>Good evening</Text>
              <Text style={styles.title}>LoGaCo</Text>
            </View>
            <TouchableOpacity style={styles.profileButton}>
              <Ionicons name="person-circle-outline" size={32} color="#00D4FF" />
            </TouchableOpacity>
          </View>

          {/* Main Card */}
          <BlurView intensity={20} style={styles.mainCard}>
            <LinearGradient
              colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
              style={styles.cardGradient}
            >
              <View style={styles.cardContent}>
                <View style={styles.iconContainer}>
                  <Ionicons name="game-controller" size={48} color="#00D4FF" />
                </View>
                <Text style={styles.cardTitle}>Ready to Connect</Text>
                <Text style={styles.cardSubtitle}>
                  Find nearby players and start gaming together
                </Text>
                <TouchableOpacity style={styles.connectButton}>
                  <Text style={styles.connectButtonText}>Start Scanning</Text>
                  <Ionicons name="arrow-forward" size={20} color="#1a1a2e" />
                </TouchableOpacity>
              </View>
            </LinearGradient>
          </BlurView>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity style={styles.actionCard}>
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="search" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Scan for Games</Text>
                </BlurView>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionCard}>
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="wifi" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Create Hotspot</Text>
                </BlurView>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionCard}>
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="bluetooth" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Bluetooth Pair</Text>
                </BlurView>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionCard}>
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="qr-code" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>QR Connect</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>

          {/* Recent Activity */}
          <View style={styles.recentActivity}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <BlurView intensity={15} style={styles.activityCard}>
              <View style={styles.activityItem}>
                <Ionicons name="time-outline" size={20} color="#00D4FF" />
                <Text style={styles.activityText}>No recent connections</Text>
              </View>
            </BlurView>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Account for tab bar
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
  },
  greeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  profileButton: {
    padding: 8,
  },
  mainCard: {
    marginHorizontal: 20,
    borderRadius: 24,
    overflow: 'hidden',
    marginBottom: 30,
  },
  cardGradient: {
    padding: 24,
  },
  cardContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 24,
  },
  connectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#00D4FF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a2e',
  },
  quickActions: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    width: (width - 56) / 2,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionBlur: {
    padding: 16,
    alignItems: 'center',
    gap: 8,
  },
  actionText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  recentActivity: {
    paddingHorizontal: 20,
  },
  activityCard: {
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  activityText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
});
