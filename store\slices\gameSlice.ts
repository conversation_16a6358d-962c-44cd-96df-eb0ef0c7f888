import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Game {
  id: string;
  name: string;
  packageName?: string;
  bundleId?: string;
  version?: string;
  icon?: string;
  players: {
    min: number;
    max: number;
  };
  status: 'compatible' | 'needs_setup' | 'incompatible' | 'unknown';
  connectionTypes: ('bluetooth' | 'wifi' | 'hotspot')[];
  lastPlayed?: number;
  playTime?: number;
  isInstalled: boolean;
  isCustom: boolean;
}

export interface GameSession {
  id: string;
  gameId: string;
  hostId: string;
  name: string;
  players: {
    id: string;
    name: string;
    status: 'joined' | 'ready' | 'playing' | 'disconnected';
  }[];
  maxPlayers: number;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'ended';
  createdAt: number;
  startedAt?: number;
  endedAt?: number;
  settings: Record<string, any>;
}

export interface GameState {
  // Installed games
  installedGames: Game[];
  customGames: Game[];
  
  // Game detection
  isScanning: boolean;
  lastScanTime: number | null;
  
  // Current session
  currentSession: GameSession | null;
  availableSessions: GameSession[];
  
  // Game history
  recentGames: Game[];
  gameHistory: {
    gameId: string;
    sessionId: string;
    playedAt: number;
    duration: number;
    players: string[];
  }[];
  
  // Filters and search
  searchQuery: string;
  selectedFilter: 'all' | 'compatible' | 'needs_setup' | 'recent';
  
  // Loading states
  isLoadingGames: boolean;
  isCreatingSession: boolean;
  isJoiningSession: boolean;
  
  // Errors
  error: string | null;
}

const initialState: GameState = {
  // Installed games
  installedGames: [
    {
      id: '1',
      name: 'Among Us',
      packageName: 'com.innersloth.spacemafia',
      players: { min: 4, max: 10 },
      status: 'compatible',
      connectionTypes: ['wifi', 'hotspot'],
      isInstalled: true,
      isCustom: false,
    },
    {
      id: '2',
      name: 'Minecraft',
      packageName: 'com.mojang.minecraftpe',
      players: { min: 2, max: 8 },
      status: 'compatible',
      connectionTypes: ['wifi', 'hotspot'],
      isInstalled: true,
      isCustom: false,
    },
    {
      id: '3',
      name: 'Call of Duty Mobile',
      packageName: 'com.activision.callofduty.shooter',
      players: { min: 2, max: 4 },
      status: 'needs_setup',
      connectionTypes: ['wifi'],
      isInstalled: true,
      isCustom: false,
    },
    {
      id: '4',
      name: 'PUBG Mobile',
      packageName: 'com.tencent.ig',
      players: { min: 2, max: 4 },
      status: 'incompatible',
      connectionTypes: [],
      isInstalled: true,
      isCustom: false,
    },
  ],
  customGames: [],
  
  // Game detection
  isScanning: false,
  lastScanTime: null,
  
  // Current session
  currentSession: null,
  availableSessions: [],
  
  // Game history
  recentGames: [],
  gameHistory: [],
  
  // Filters and search
  searchQuery: '',
  selectedFilter: 'all',
  
  // Loading states
  isLoadingGames: false,
  isCreatingSession: false,
  isJoiningSession: false,
  
  // Errors
  error: null,
};

const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    // Game scanning
    startGameScan: (state) => {
      state.isScanning = true;
      state.lastScanTime = Date.now();
      state.error = null;
    },
    
    stopGameScan: (state) => {
      state.isScanning = false;
    },
    
    // Game management
    addInstalledGame: (state, action: PayloadAction<Game>) => {
      const existingIndex = state.installedGames.findIndex(
        game => game.id === action.payload.id
      );
      
      if (existingIndex >= 0) {
        state.installedGames[existingIndex] = action.payload;
      } else {
        state.installedGames.push(action.payload);
      }
    },
    
    removeInstalledGame: (state, action: PayloadAction<string>) => {
      state.installedGames = state.installedGames.filter(
        game => game.id !== action.payload
      );
    },
    
    updateGameStatus: (state, action: PayloadAction<{ id: string; status: Game['status'] }>) => {
      const { id, status } = action.payload;
      
      const gameIndex = state.installedGames.findIndex(game => game.id === id);
      if (gameIndex >= 0) {
        state.installedGames[gameIndex].status = status;
      }
      
      const customIndex = state.customGames.findIndex(game => game.id === id);
      if (customIndex >= 0) {
        state.customGames[customIndex].status = status;
      }
    },
    
    addCustomGame: (state, action: PayloadAction<Omit<Game, 'id' | 'isCustom'>>) => {
      const newGame: Game = {
        ...action.payload,
        id: Date.now().toString(),
        isCustom: true,
      };
      state.customGames.push(newGame);
    },
    
    removeCustomGame: (state, action: PayloadAction<string>) => {
      state.customGames = state.customGames.filter(
        game => game.id !== action.payload
      );
    },
    
    // Session management
    createSession: (state, action: PayloadAction<Omit<GameSession, 'id' | 'createdAt' | 'status'>>) => {
      state.isCreatingSession = true;
      state.error = null;
    },
    
    sessionCreated: (state, action: PayloadAction<GameSession>) => {
      state.currentSession = action.payload;
      state.isCreatingSession = false;
    },
    
    joinSession: (state, action: PayloadAction<string>) => {
      state.isJoiningSession = true;
      state.error = null;
    },
    
    sessionJoined: (state, action: PayloadAction<GameSession>) => {
      state.currentSession = action.payload;
      state.isJoiningSession = false;
    },
    
    leaveSession: (state) => {
      if (state.currentSession) {
        // Add to history before leaving
        const historyEntry = {
          gameId: state.currentSession.gameId,
          sessionId: state.currentSession.id,
          playedAt: Date.now(),
          duration: state.currentSession.startedAt 
            ? Date.now() - state.currentSession.startedAt 
            : 0,
          players: state.currentSession.players.map(p => p.name),
        };
        
        state.gameHistory.unshift(historyEntry);
        if (state.gameHistory.length > 50) {
          state.gameHistory = state.gameHistory.slice(0, 50);
        }
      }
      
      state.currentSession = null;
    },
    
    updateSession: (state, action: PayloadAction<Partial<GameSession>>) => {
      if (state.currentSession) {
        state.currentSession = { ...state.currentSession, ...action.payload };
      }
    },
    
    addAvailableSession: (state, action: PayloadAction<GameSession>) => {
      const existingIndex = state.availableSessions.findIndex(
        session => session.id === action.payload.id
      );
      
      if (existingIndex >= 0) {
        state.availableSessions[existingIndex] = action.payload;
      } else {
        state.availableSessions.push(action.payload);
      }
    },
    
    removeAvailableSession: (state, action: PayloadAction<string>) => {
      state.availableSessions = state.availableSessions.filter(
        session => session.id !== action.payload
      );
    },
    
    clearAvailableSessions: (state) => {
      state.availableSessions = [];
    },
    
    // Search and filters
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    setSelectedFilter: (state, action: PayloadAction<GameState['selectedFilter']>) => {
      state.selectedFilter = action.payload;
    },
    
    // Recent games
    addRecentGame: (state, action: PayloadAction<Game>) => {
      // Remove if already exists
      state.recentGames = state.recentGames.filter(
        game => game.id !== action.payload.id
      );
      
      // Add to beginning
      state.recentGames.unshift(action.payload);
      
      // Keep only last 10
      if (state.recentGames.length > 10) {
        state.recentGames = state.recentGames.slice(0, 10);
      }
    },
    
    // Error handling
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isCreatingSession = false;
      state.isJoiningSession = false;
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  startGameScan,
  stopGameScan,
  addInstalledGame,
  removeInstalledGame,
  updateGameStatus,
  addCustomGame,
  removeCustomGame,
  createSession,
  sessionCreated,
  joinSession,
  sessionJoined,
  leaveSession,
  updateSession,
  addAvailableSession,
  removeAvailableSession,
  clearAvailableSessions,
  setSearchQuery,
  setSelectedFilter,
  addRecentGame,
  setError,
  clearError,
} = gameSlice.actions;

export default gameSlice.reducer;
